#!/usr/bin/env python3

import requests
import sys

def test_registration():
    """Test the registration endpoint"""
    url = "http://127.0.0.1:5000/register"
    
    # Test with empty data
    print("Testing registration with empty data...")
    response = requests.post(url, data={})
    print(f"Status Code: {response.status_code}")
    if "Name is required" in response.text:
        print("ERROR: Found 'Name is required' message!")
    if "Username is required" in response.text:
        print("Found 'Username is required' message")
    
    # Test with valid data
    print("\nTesting registration with valid data...")
    test_data = {
        'username': 'testuser123',
        'email': '<EMAIL>',
        'password': 'password123',
        'confirm_password': 'password123'
    }
    response = requests.post(url, data=test_data)
    print(f"Status Code: {response.status_code}")
    if "Registration successful" in response.text:
        print("Registration successful!")
    elif "Username already taken" in response.text:
        print("Username already taken")
    else:
        print("Unknown response")

if __name__ == "__main__":
    test_registration()
