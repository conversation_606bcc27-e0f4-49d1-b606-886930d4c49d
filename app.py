from flask import Flask, render_template, request, jsonify, redirect, url_for, session, flash
import google.generativeai as genai
import os
import sqlite3
import json
import secrets
from datetime import datetime, timedelta
from dotenv import load_dotenv
from urllib.parse import urlencode
from flask_session import Session  # Import Flask-Session
from database import (
    init_db,
    create_conversation,
    save_message,
    get_conversations,
    get_conversation_messages,
    update_conversation_title,
    delete_conversation,
    toggle_save_conversation,
    register_user,
    verify_user_by_unique_id, # Changed
    get_user_by_unique_id,    # New
    get_user_by_id,
    create_conversation_for_user,
    get_user_conversations,
    get_message_count,
    get_conversation_summary,
    update_user_profile,
    get_user_profile,
    # New friend-related functions
    send_friend_request,
    accept_friend_request,
    decline_friend_request,
    get_pending_friend_requests,
    get_friends_list,
    get_friendship_status,
    # New private chat functions
    get_private_chat_id,
    create_private_chat,
    save_private_message,
    get_private_messages,
    get_user_private_chats,
    # New group chat functions
    create_group_chat,
    add_group_member,
    remove_group_member,
    get_group_members,
    save_group_message,
    get_group_messages,
    get_user_group_chats,
    get_group_chat_by_id
)
from thread_manager import init_thread_db, save_thread, get_thread, delete_thread

app = Flask(__name__, template_folder='templates')
# Use a fixed secret key instead of random one to ensure sessions persist across server restarts
app.secret_key = os.getenv('SECRET_KEY', 'voyager_ai_secret_key_for_sessions')
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=30)  # Set session lifetime to 30 days

# Server-side session configuration
app.config['SESSION_TYPE'] = 'filesystem'  # Store sessions on filesystem
app.config['SESSION_FILE_DIR'] = os.path.join(os.getcwd(), 'flask_session')  # Directory to store session files
app.config['SESSION_COOKIE_SECURE'] = False  # Set to True in production with HTTPS
app.config['SESSION_COOKIE_HTTPONLY'] = True  # Prevent JavaScript access to session cookie
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'  # Restrict cookie sending to same-site requests
app.config['SESSION_COOKIE_NAME'] = 'voyager_session'  # Custom session cookie name
app.config['SESSION_USE_SIGNER'] = True  # Sign the session cookie for added security
app.config['SESSION_REFRESH_EACH_REQUEST'] = True  # Refresh the session cookie on each request
app.config['SESSION_PERMANENT'] = True  # Make sessions permanent by default

# Initialize Flask-Session
Session(app)

load_dotenv()
init_db()
init_thread_db()

# Create session directory if it doesn't exist
os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)

def create_test_user():
    """Create a test user if it doesn't exist"""
    try:
        test_user = get_user_by_unique_id('testuser') # Use simple username as unique_id
        if not test_user:
            print("Creating test user...")
            user_id, unique_id = register_user('testuser')
            if user_id:
                print(f"Test user created with ID: {user_id}, Unique ID: {unique_id}")
            else:
                print("Failed to create test user")
        else:
            print(f"Test user already exists with ID: {test_user[0]}, Unique ID: {test_user[2]}")
    except Exception as e:
        print(f"Error with test user: {e}")
        # Try to create a new test user anyway
        try:
            user_id, unique_id = register_user('testuser')
            if user_id:
                print(f"Test user created with ID: {user_id}, Unique ID: {unique_id}")
        except Exception as e2:
            print(f"Failed to create test user: {e2}")

# Create a custom user function for easier registration
def create_custom_user(name):
    """Create a custom user"""
    # This function will now generate a unique_id automatically
    print(f"Creating new user '{name}'...")
    user_id, unique_id = register_user(name)
    if user_id:
        print(f"User '{name}' created with ID: {user_id}, Unique ID: {unique_id}")
        return user_id, unique_id
    else:
        print(f"Failed to create user '{name}'")
        return None, None

# Create the test user
create_test_user()

# Create a custom user for the current session
# This will create a user with the name 'Royox'
create_custom_user('Royox')

# App configuration

# Simple in-memory cache for thread loading
thread_cache = {}
MAX_CACHE_SIZE = 10  # Maximum number of threads to cache

def init_genai():
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError("GOOGLE_API_KEY not found in environment variables")

    print("\n=== Debug Information ===")
    print("API Key loaded:", api_key[:8] + "..." if api_key else "Not found")
    genai.configure(api_key=api_key)

    # Using Gemini 2.0 Flash directly since we know it's available
    model_name = "models/gemini-2.0-flash"
    print(f"\nUsing model: {model_name}")
    return api_key, model_name

class GeminiChatbot:
    def __init__(self, api_key: str, model_name: str):
        self.api_key = api_key
        self.model_name = model_name
        try:
            print(f"\nTrying to initialize with model: {model_name}")
            self.model = genai.GenerativeModel(model_name)
            self.restart_chat()
            print("Model initialized successfully")
        except Exception as e:
            print(f"Error initializing model: {str(e)}")
            raise

    def restart_chat(self):
        self.chat = self.model.start_chat(history=[])
        # Updated personality prompt with name
        self.chat.send_message(
            "You are Voyager, an AI assistant. Your name is Voyager. When asked about your name or identity, always identify yourself as Voyager. "
            "Begin your first response to a user with 'Hello! I'm Voyager, your AI assistant.' "
            "Be polite, friendly, and helpful in your responses. Keep your responses focused and relevant. "
            "Use a warm, conversational tone while maintaining professionalism. "
            "While you should acknowledge your name when asked, avoid introducing yourself in every message after the first one."
        )

    def generate_response(self, user_input: str, conversation_id=None) -> str:
        try:
            response = self.chat.send_message(user_input)

            # Save thread if conversation_id is provided
            if conversation_id:
                self.save_thread(conversation_id)

            return response.text
        except Exception as e:
            error_msg = f"Error generating response: {str(e)}"
            print(error_msg)
            return error_msg

    def clear_history(self):
        self.chat = self.model.start_chat(history=[])

    def get_serializable_history(self):
        """Convert chat history to a serializable format"""
        history = []
        for message in self.chat.history:
            history.append({
                'role': message.role,
                'parts': [{'text': part.text} for part in message.parts]
            })
        return history

    def load_from_serialized_history(self, history):
        """Load chat from serialized history"""
        try:
            # Create a new chat with the model
            self.chat = self.model.start_chat(history=[])

            # First send the system prompt
            system_prompt = "You are Voyager, an AI assistant. Your name is Voyager. When asked about your name or identity, always identify yourself as Voyager. Begin your first response to a user with 'Hello! I'm Voyager, your AI assistant.' Be polite, friendly, and helpful in your responses. Keep your responses focused and relevant. Use a warm, conversational tone while maintaining professionalism. While you should acknowledge your name when asked, avoid introducing yourself in every message after the first one."
            self.chat.send_message(system_prompt)

            # Validate the history format
            if not history or not isinstance(history, list):
                print(f"Invalid history format: {type(history)}")
                return False
            
            # Prepare a clean history list
            clean_history = []

            # First pass: identify valid messages and skip duplicates of system message
            for msg in history:
                # Skip invalid messages
                if not isinstance(msg, dict) or 'role' not in msg:
                    continue

                # Skip system messages (we already sent one)
                if msg['role'] == 'model' and 'parts' in msg and isinstance(msg['parts'], list) and len(msg['parts']) > 0:
                    if isinstance(msg['parts'][0], dict) and 'text' in msg['parts'][0]:
                        if 'You are Voyager' in msg['parts'][0]['text']:
                            # Skip system messages
                            continue

                # Add valid messages to our clean history
                clean_history.append(msg)

            # Only process the last few messages to avoid context length issues
            max_messages = 20  # Limit to last 20 messages for performance
            recent_messages = clean_history[-max_messages:] if len(clean_history) > max_messages else clean_history

            print(f"Loading {len(recent_messages)} messages from history")

            # Build message pairs for more efficient processing
            message_pairs = []
            i = 0
            while i < len(recent_messages):
                if i + 1 < len(recent_messages) and recent_messages[i]['role'] == 'user' and recent_messages[i+1]['role'] == 'model':
                    # We have a user-model pair
                    message_pairs.append((recent_messages[i], recent_messages[i+1]))
                    i += 2
                else:
                    # Single message, just add it
                    message_pairs.append((recent_messages[i], None))
                    i += 1

            # Process message pairs
            for user_msg, model_msg in message_pairs:
                try:
                    # Extract user text safely
                    user_text = self._extract_message_text(user_msg, "[Message content unavailable]")

                    if model_msg:
                        # We have both user and model messages
                        model_text = self._extract_message_text(model_msg, "[Response content unavailable]")

                        # Add both to history without API call
                        self.chat.history.append({
                            'role': 'user',
                            'parts': [{'text': user_text}]
                        })
                        self.chat.history.append({
                            'role': 'model',
                            'parts': [{'text': model_text}]
                        })
                    else:
                        # Only user message, send it to get a response
                        self.chat.send_message(user_text)
                except Exception as e:
                    print(f"Error processing message pair: {str(e)}")
                    continue

            return True
        except Exception as e:
            print(f"Error loading chat history: {str(e)}")
            return False

    def _extract_message_text(self, msg, default_text=""):
        """Helper method to safely extract text from a message"""
        try:
            if 'parts' in msg and isinstance(msg['parts'], list) and len(msg['parts']) > 0:
                part = msg['parts'][0]
                if isinstance(part, dict) and 'text' in part:
                    return part['text']
                else:
                    return str(part)
            return default_text
        except Exception as e:
            print(f"Error extracting message text: {str(e)}")
            return default_text

    def save_thread(self, conversation_id):
        """Save the current thread to the database"""
        thread_data = {
            'history': self.get_serializable_history(),
            'model': self.model_name
        }
        save_thread(conversation_id, thread_data)

        # Update the cache with the current chat
        self._update_thread_cache(conversation_id)

    def load_thread(self, conversation_id):
        """Load a thread from the database"""
        global thread_cache

        try:
            # Check if thread is in cache
            cache_key = f"thread_{conversation_id}"
            if cache_key in thread_cache:
                print(f"Loading thread {conversation_id} from cache")
                # Restore chat from cached history
                self.chat = thread_cache[cache_key]
                return True

            # First check if we have a valid thread in the database
            start_time = datetime.now()
            thread_data = get_thread(conversation_id)
            db_time = (datetime.now() - start_time).total_seconds()
            print(f"Database fetch took {db_time:.3f} seconds")

            if thread_data and isinstance(thread_data, dict) and 'history' in thread_data:
                print(f"Found thread data for conversation {conversation_id}")

                # Quick validation of the history data
                if not isinstance(thread_data['history'], list):
                    print(f"Thread history is not a list: {type(thread_data['history'])}")
                    # Get messages only if we need to rebuild
                    messages = get_conversation_messages(conversation_id)
                    return self.rebuild_thread_from_messages(conversation_id, messages)

                # Check if the thread history is valid
                try:
                    # Count valid messages but don't create a new list (more efficient)
                    valid_count = sum(1 for msg in thread_data['history']
                                    if isinstance(msg, dict) and 'role' in msg and msg['role'] in ['user', 'model'])

                    print(f"Thread history contains {valid_count} valid messages")

                    # If we have a reasonable number of valid messages, load the thread
                    if valid_count > 0:
                        # Load the thread history
                        start_time = datetime.now()
                        result = self.load_from_serialized_history(thread_data['history'])
                        load_time = (datetime.now() - start_time).total_seconds()
                        print(f"Thread loading took {load_time:.3f} seconds")

                        if result:
                            # Cache the thread for future use
                            self._update_thread_cache(conversation_id)
                            return True

                    # If we get here, we need to rebuild the thread
                    messages = get_conversation_messages(conversation_id)
                    return self.rebuild_thread_from_messages(conversation_id, messages)
                except Exception as e:
                    print(f"Error processing thread history: {str(e)}")
                    messages = get_conversation_messages(conversation_id)
                    return self.rebuild_thread_from_messages(conversation_id, messages)
            else:
                print(f"No valid thread data found for conversation {conversation_id}")
                messages = get_conversation_messages(conversation_id)
                return self.rebuild_thread_from_messages(conversation_id, messages)
        except Exception as e:
            print(f"Error in load_thread: {str(e)}")
            return False

    def _update_thread_cache(self, conversation_id):
        """Update the thread cache with the current chat"""
        global thread_cache, MAX_CACHE_SIZE

        # Add current chat to cache
        cache_key = f"thread_{conversation_id}"
        thread_cache[cache_key] = self.chat

        # If cache is too large, remove oldest entries
        if len(thread_cache) > MAX_CACHE_SIZE:
            # Get the oldest keys (we'll keep the most recent ones)
            keys_to_remove = list(thread_cache.keys())[:-MAX_CACHE_SIZE]
            for key in keys_to_remove:
                thread_cache.pop(key, None)

    def rebuild_thread_from_messages(self, conversation_id, messages):
        """Rebuild a thread from database messages"""
        try:
            print(f"Rebuilding thread for conversation {conversation_id} from {len(messages)} messages")

            # Restart the chat to clear any existing state
            self.restart_chat()

            # Create a new history list to build up
            new_history = []

            # Process messages in pairs to rebuild the thread
            for i in range(0, len(messages), 2):
                if i < len(messages):
                    user_msg = messages[i]
                    # Only process if this is a user message
                    if user_msg[1]:  # is_user
                        user_content = user_msg[0]

                        # Check if there's a corresponding bot response
                        if i + 1 < len(messages) and not messages[i+1][1]:  # not is_user
                            # We have both user message and bot response
                            print(f"Adding message pair: User: {user_content[:30]}...")

                            # Create user message dict
                            user_dict = {
                                'role': 'user',
                                'parts': [{'text': user_content}]
                            }

                            # Create model response dict
                            model_dict = {
                                'role': 'model',
                                'parts': [{'text': messages[i+1][0]}]
                            }

                            # Add to our new history list
                            new_history.append(user_dict)
                            new_history.append(model_dict)

            # Now set the chat history directly instead of appending
            if new_history:
                # First add the system message
                system_message = {
                    'role': 'model',
                    'parts': [{'text': "You are Voyager, an AI assistant. Your name is Voyager. When asked about your name or identity, always identify yourself as Voyager. Begin your first response to a user with 'Hello! I'm Voyager, your AI assistant.' Be polite, friendly, and helpful in your responses. Keep your responses focused and relevant. Use a warm, conversational tone while maintaining professionalism. While you should acknowledge your name when asked, avoid introducing yourself in every message after the first one."}]
                }

                # Set the chat history with the system message first, then our rebuilt history
                self.chat.history = [system_message] + new_history

                # Save the rebuilt thread
                self.save_thread(conversation_id)
                return True
            else:
                print("No valid message pairs found to rebuild thread")
                return False
        except Exception as e:
            print(f"Error rebuilding thread: {str(e)}")
            return False

try:
    api_key, model_name = init_genai()
    chatbot = GeminiChatbot(api_key, model_name)
except Exception as e:
    print(f"\nFailed to initialize: {str(e)}")
    raise

# Authentication check decorator
def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            # Store the requested URL for redirecting after login
            next_url = request.url
            flash('Please log in to access this page.', 'info')
            return redirect(url_for('login', next=next_url))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/')
def landing():
    # Check if user is logged in to show different content
    is_logged_in = 'user_id' in session
    return render_template('landing.html', is_logged_in=is_logged_in)

@app.route('/dashboard')
@login_required
def dashboard():
    user_id = session.get('user_id')
    conversations = get_user_conversations(user_id)
    # Convert conversations to a format the template can use
    formatted_conversations = [
        {
            'id': conv[0],
            'title': conv[1],
            'created_at': conv[2],
            'is_saved': conv[3]
        }
        for conv in conversations
    ]
    return render_template('index.html', conversations=formatted_conversations, session=session)

@app.route('/register', methods=['GET', 'POST'])
def register():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        print("=== REGISTRATION DEBUG ===")
        print("Form data received:", dict(request.form))
        print("Request method:", request.method)
        print("Request headers:", dict(request.headers))

        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        print(f"Username (from form): '{username}'")
        print(f"Email (from form): '{email}'")
        print(f"Password (from form): '{password}'")
        print(f"Confirm Password (from form): '{confirm_password}'")

        # Validation
        if not username:
            print("ERROR: Username is missing or empty after form submission!")
            flash('Username is required', 'error')
            return render_template('register.html')

        if not email:
            flash('Email is required', 'error')
            return render_template('register.html')

        if not password:
            flash('Password is required', 'error')
            return render_template('register.html')

        if not confirm_password:
            flash('Please confirm your password', 'error')
            return render_template('register.html')

        if password != confirm_password:
            flash('Passwords do not match', 'error')
            return render_template('register.html')

        # Use username as the name for registration
        print(f"Attempting to register user: {username}")
        user_id, unique_id = register_user(username)
        print(f"Registration result - user_id: {user_id}, unique_id: {unique_id}")

        if user_id:
            session['user_id'] = user_id
            session['name'] = username
            session['username'] = username
            print(f"Registration successful for {username}")
            flash(f'Registration successful! You can now login with username: {username}', 'success')
            next_url = request.args.get('next')
            if next_url:
                return redirect(next_url)
            else:
                return redirect(url_for('dashboard'))
        else:
            # Check if the username was actually provided but registration failed (e.g., username taken)
            if username:
                print(f"Registration failed for {username}: Username already taken.")
                flash('Username already taken. Please choose a different username.', 'error')
            else:
                # This case should ideally be caught by the initial 'if not username:' check,
                # but as a fallback, if it reaches here without a username, it's a missing username.
                print("Registration failed: Username is missing (fallback).")
                flash('Username is required', 'error')

    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        email = request.form.get('email')

        if not email:
            flash('Email is required', 'error')
            return render_template('login.html')

        user_id = verify_user_by_unique_id(email)

        if user_id:
            user_profile = get_user_profile(user_id)
            session['user_id'] = user_id
            session['name'] = user_profile['name']
            session['username'] = user_profile['unique_id']

            flash('Login successful!', 'success')
            next_url = request.args.get('next')
            if next_url:
                return redirect(next_url)
            else:
                return redirect(url_for('dashboard'))
        else:
            flash('Username not found. Please check your username or register a new account.', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

@app.route('/profile')
@login_required
def profile():
    user_id = session.get('user_id')
    user_profile = get_user_profile(user_id)

    if user_profile:
        session['profile_pic'] = user_profile.get('profile_pic')
        session['birthdate'] = user_profile.get('birthdate')
        session['hobbies'] = user_profile.get('hobbies')
        session['name'] = user_profile.get('name')
        session['username'] = user_profile.get('unique_id')
        session['created_at'] = user_profile.get('created_at')

        session.modified = True

    return render_template('profile.html', session=session)

@app.route('/update_profile', methods=['POST'])
@login_required
def update_profile():
    user_id = session.get('user_id')

    profile_data = {
        'name': request.form.get('display_name'),
        'birthdate': request.form.get('birthdate'),
        'hobbies': request.form.get('hobbies')
    }

    if 'profile_pic' in request.files:
        file = request.files['profile_pic']
        if file and file.filename:
            import base64
            file_content = file.read()
            encoded_content = base64.b64encode(file_content).decode('utf-8')
            file_type = file.content_type
            profile_data['profile_pic'] = f'data:{file_type};base64,{encoded_content}'

    success = update_user_profile(user_id, profile_data)

    if success:
        if 'name' in profile_data and profile_data['name']:
            session['name'] = profile_data['name']
        if 'profile_pic' in profile_data and profile_data['profile_pic']:
            session['profile_pic'] = profile_data['profile_pic']
        if 'birthdate' in profile_data:
            session['birthdate'] = profile_data['birthdate']
        if 'hobbies' in profile_data:
            session['hobbies'] = profile_data['hobbies']

        flash('Profile updated successfully!', 'success')
    else:
        flash('Failed to update profile.', 'error')

    return redirect(url_for('profile'))

@app.route('/update_chat_title', methods=['POST'])
@login_required
def update_chat_title():
    data = request.json
    chat_id = data.get('chat_id')
    new_title = data.get('new_title')
    user_id = session.get('user_id')

    if not chat_id or not new_title:
        return jsonify({'success': False, 'error': 'Missing parameters'}), 400

    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id FROM conversations WHERE id = ? AND user_id = ?', (chat_id, user_id))
    conversation = c.fetchone()
    conn.close()

    if not conversation:
        return jsonify({'success': False, 'error': 'Not authorized'}), 403

    update_conversation_title(chat_id, new_title)
    return jsonify({'success': True})

@app.route('/delete_chat/<int:chat_id>', methods=['DELETE'])
@login_required
def delete_chat(chat_id):
    user_id = session.get('user_id')
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id FROM conversations WHERE id = ? AND user_id = ?', (chat_id, user_id))
    conversation = c.fetchone()
    conn.close()

    if not conversation:
        return jsonify({'success': False, 'error': 'Not authorized'}), 403

    delete_thread(chat_id)
    delete_conversation(chat_id)
    return jsonify({'success': True})

@app.route('/toggle_save_chat/<int:chat_id>', methods=['POST'])
@login_required
def toggle_save_chat(chat_id):
    user_id = session.get('user_id')
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT id FROM conversations WHERE id = ? AND user_id = ?', (chat_id, user_id))
    conversation = c.fetchone()

    if not conversation:
        conn.close()
        return jsonify({'success': False, 'error': 'Not authorized'}), 403

    toggle_save_conversation(chat_id)

    c.execute('SELECT is_saved FROM conversations WHERE id = ?', (chat_id,))
    is_saved = c.fetchone()[0]
    conn.close()

    return jsonify({
        'success': True,
        'is_saved': bool(is_saved)
    })

def is_greeting(text):
    text = text.lower().strip()
    greetings = ['hi', 'hello', 'hey', 'greetings', 'good morning', 'good afternoon', 'good evening', 'yo', 'hiya', 'howdy']

    if any(text == greeting or text.startswith(greeting + ' ') for greeting in greetings):
        return True

    greeting_phrases = ['how are you', 'what\'s up', 'nice to meet you']
    if any(phrase in text for phrase in greeting_phrases):
        return True

    return False

@app.route('/chat', methods=['POST'])
@login_required
def chat():
    user_message = request.json.get('message', '')
    conversation_id = request.json.get('conversation_id')
    chat_type = request.json.get('chat_type', 'general') # 'general', 'private', 'group'
    recipient_id = request.json.get('recipient_id') # For private/group chats
    user_id = session.get('user_id')

    if chat_type == 'general':
        if not conversation_id:
            title_prompt = f"Based on this first message: '{user_message}', generate a very brief (max 4 words) title for this conversation. Response should be just the title, nothing else."
            title = chatbot.generate_response(title_prompt).strip()
            if "error" in title.lower():
                title = user_message[:30] + "..." if len(user_message) > 30 else user_message
            conversation_id = create_conversation_for_user(title, user_id)

        save_message(conversation_id, user_message, True)

        if is_greeting(user_message):
            conn = sqlite3.connect('chats.db')
            c = conn.cursor()
            c.execute('''
                SELECT COUNT(*) FROM messages
                WHERE conversation_id = ? AND is_user = 1 AND (
                    content LIKE 'hi%' OR content LIKE 'hello%' OR content LIKE 'hey%' OR
                    content LIKE 'good morning%' OR content LIKE 'good afternoon%' OR
                    content LIKE 'good evening%' OR content LIKE 'yo%' OR content LIKE 'hiya%' OR
                    content LIKE 'howdy%' OR content LIKE "what's up%" OR content LIKE 'how are you%'
                )
            ''', (conversation_id,))
            greeting_count = c.fetchone()[0]
            conn.close()

            if greeting_count <= 1:
                response = chatbot.generate_response(user_message, conversation_id)
                save_message(conversation_id, response, False)
                return jsonify({
                    'response': response,
                    'thinking': False,
                    'conversation_id': conversation_id,
                    'chat_type': 'general'
                })
            elif greeting_count == 2:
                response = "I believe we've already greeted each other. Is there something specific I can help you with today?"
                save_message(conversation_id, response, False)
                return jsonify({
                    'response': response,
                    'thinking': False,
                    'conversation_id': conversation_id,
                    'chat_type': 'general'
                })
            elif greeting_count == 3:
                response = "You've said hello several times now. Are you testing me, or is there something else you'd like to discuss?"
                save_message(conversation_id, response, False)
                return jsonify({
                    'response': response,
                    'thinking': False,
                    'conversation_id': conversation_id,
                    'chat_type': 'general'
                })
            elif greeting_count == 4:
                response = "I notice you keep greeting me. Is this a prank or are you checking if I'm responding properly? I'm here to assist with actual questions or tasks if you have any."
                save_message(conversation_id, response, False)
                return jsonify({
                    'response': response,
                    'thinking': False,
                    'conversation_id': conversation_id,
                    'chat_type': 'general'
                })
            else:
                response = "I've noticed you've greeted me " + str(greeting_count) + " times now. While I'm happy to chat, I'm designed to be helpful with tasks and questions. Is there something specific you need assistance with?"
                save_message(conversation_id, response, False)
                return jsonify({
                    'response': response,
                    'thinking': False,
                    'conversation_id': conversation_id,
                    'chat_type': 'general'
                })
        
        return jsonify({
            'response': '',
            'thinking': True,
            'conversation_id': conversation_id,
            'chat_type': 'general'
        })

    elif chat_type == 'private':
        if not recipient_id:
            return jsonify({'success': False, 'error': 'Recipient ID missing for private chat'}), 400
        
        private_chat_id = get_private_chat_id(user_id, recipient_id)
        if not private_chat_id:
            private_chat_id = create_private_chat(user_id, recipient_id)
            if not private_chat_id:
                return jsonify({'success': False, 'error': 'Failed to create private chat'}), 500
        
        save_private_message(private_chat_id, user_id, user_message)
        
        # For private chats, we don't generate AI responses automatically unless tagged
        # This is a placeholder for future AI integration in private chats
        response_content = "Message sent."
        if "@voyager" in user_message.lower():
            ai_response = chatbot.generate_response(user_message.replace("@voyager", "").strip())
            save_private_message(private_chat_id, 0, ai_response) # Sender ID 0 for AI
            response_content = ai_response

        return jsonify({
            'response': response_content,
            'thinking': False,
            'conversation_id': private_chat_id,
            'chat_type': 'private',
            'recipient_id': recipient_id
        })

    elif chat_type == 'group':
        if not recipient_id: # recipient_id here is group_chat_id
            return jsonify({'success': False, 'error': 'Group Chat ID missing'}), 400
        
        group_chat_id = recipient_id
        
        save_group_message(group_chat_id, user_id, user_message)

        response_content = "Message sent to group."
        if "@voyager" in user_message.lower():
            ai_response = chatbot.generate_response(user_message.replace("@voyager", "").strip())
            save_group_message(group_chat_id, 0, ai_response, is_ai_message=True) # Sender ID 0 for AI
            response_content = ai_response

        return jsonify({
            'response': response_content,
            'thinking': False,
            'conversation_id': group_chat_id,
            'chat_type': 'group',
            'recipient_id': group_chat_id
        })

    return jsonify({'success': False, 'error': 'Invalid chat type'}), 400

@app.route('/get_response', methods=['POST'])
def get_response():
    user_message = request.json.get('message', '')
    conversation_id = request.json.get('conversation_id')
    chat_type = request.json.get('chat_type', 'general') # 'general', 'private', 'group'

    if chat_type == 'general':
        response = chatbot.generate_response(user_message, conversation_id)
        save_message(conversation_id, response, False)

        message_count = get_message_count(conversation_id)
        title_updated = False
        new_title = ""

        if message_count in [6, 12, 20, 30]:
            conversation_summary = get_conversation_summary(conversation_id)
            title_prompt = f"Based on this conversation summary, generate a very brief (max 4 words) title that captures the main topic. Response should be just the title, nothing else:\n\n{conversation_summary}"
            new_title = chatbot.generate_response(title_prompt).strip()

            if "error" in new_title.lower() or len(new_title) > 30:
                title_updated = False
            else:
                update_conversation_title(conversation_id, new_title)
                title_updated = True

        return jsonify({
            'response': response,
            'thinking': False,
            'conversation_id': conversation_id,
            'title_updated': title_updated,
            'new_title': new_title,
            'chat_type': 'general'
        })
    elif chat_type == 'private':
        # AI response for private chat is handled in the /chat route if @voyager is tagged
        return jsonify({
            'response': "AI response for private chat is handled on message send if tagged.",
            'thinking': False,
            'chat_type': 'private'
        })
    elif chat_type == 'group':
        # AI response for group chat is handled in the /chat route if @voyager is tagged
        return jsonify({
            'response': "AI response for group chat is handled on message send if tagged.",
            'thinking': False,
            'chat_type': 'group'
        })
    
    return jsonify({'success': False, 'error': 'Invalid chat type for get_response'}), 400


@app.route('/load_conversation/<int:conversation_id>')
@login_required
def load_conversation(conversation_id):
    try:
        start_time = datetime.now()
        user_id = session.get('user_id')

        conn = sqlite3.connect('chats.db')
        conn.execute('PRAGMA busy_timeout = 5000')
        c = conn.cursor()
        c.execute('SELECT id, title FROM conversations WHERE id = ? AND user_id = ?', (conversation_id, user_id))
        conversation = c.fetchone()
        conn.close()

        if not conversation:
            return jsonify({
                'error': f'Conversation with ID {conversation_id} not found or not authorized',
                'messages': [],
                'thread_loaded': False
            }), 404

        thread_start = datetime.now()
        thread_loaded = chatbot.load_thread(conversation_id)
        thread_time = (datetime.now() - thread_start).total_seconds()
        print(f"Thread loading took {thread_time:.3f} seconds")

        if not thread_loaded:
            chatbot.restart_chat()
            print(f"Restarted chat for conversation {conversation_id}")

        msg_start = datetime.now()
        messages = get_conversation_messages(conversation_id)
        msg_time = (datetime.now() - msg_start).total_seconds()
        print(f"Retrieved {len(messages)} messages in {msg_time:.3f} seconds")

        formatted_messages = [
            {
                'content': msg[0],
                'is_user': msg[1],
                'timestamp': msg[2]
            }
            for msg in messages
        ]

        total_time = (datetime.now() - start_time).total_seconds()
        print(f"Total conversation loading time: {total_time:.3f} seconds")

        return jsonify({
            'messages': formatted_messages,
            'thread_loaded': thread_loaded,
            'title': conversation[1] if conversation else ''
        })
    except Exception as e:
        print(f"Error loading conversation {conversation_id}: {str(e)}")
        return jsonify({
            'error': str(e),
            'messages': [],
            'thread_loaded': False
        }), 500

@app.route('/reset_chat', methods=['POST'])
@login_required
def reset_chat():
    chatbot.restart_chat()
    return jsonify({'success': True})

# New Friend Routes
@app.route('/friends', methods=['GET'])
@login_required
def friends():
    user_id = session.get('user_id')
    friends_list = get_friends_list(user_id)
    pending_requests = get_pending_friend_requests(user_id)
    return render_template('friends.html', friends=friends_list, pending_requests=pending_requests, session=session)

@app.route('/send_friend_request', methods=['POST'])
@login_required
def send_friend_request_route():
    data = request.json
    receiver_username = data.get('username')
    sender_id = session.get('user_id')

    if not receiver_username:
        return jsonify({'success': False, 'error': 'Username is required'}), 400

    success, message = send_friend_request(sender_id, receiver_username)
    return jsonify({'success': success, 'message': message})

@app.route('/accept_friend_request', methods=['POST'])
@login_required
def accept_friend_request_route():
    data = request.json
    request_id = data.get('request_id')
    user_id = session.get('user_id') # Ensure the request is for the current user

    # Verify the request belongs to the current user before accepting
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT friend_id FROM friends WHERE id = ? AND status = ?', (request_id, 'pending'))
    req_friend_id = c.fetchone()
    conn.close()

    if not req_friend_id or req_friend_id[0] != user_id:
        return jsonify({'success': False, 'error': 'Unauthorized or request not found'}), 403

    success, message = accept_friend_request(request_id)
    return jsonify({'success': success, 'message': message})

@app.route('/decline_friend_request', methods=['POST'])
@login_required
def decline_friend_request_route():
    data = request.json
    request_id = data.get('request_id')
    user_id = session.get('user_id') # Ensure the request is for the current user

    # Verify the request belongs to the current user before declining
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT friend_id FROM friends WHERE id = ? AND status = ?', (request_id, 'pending'))
    req_friend_id = c.fetchone()
    conn.close()

    if not req_friend_id or req_friend_id[0] != user_id:
        return jsonify({'success': False, 'error': 'Unauthorized or request not found'}), 403

    success, message = decline_friend_request(request_id)
    return jsonify({'success': success, 'message': message})

# New Private Chat Routes
@app.route('/private_chats', methods=['GET'])
@login_required
def private_chats():
    user_id = session.get('user_id')
    chats = get_user_private_chats(user_id)
    return render_template('private_chats.html', private_chats=chats, session=session)

@app.route('/load_private_chat/<int:chat_id>', methods=['GET'])
@login_required
def load_private_chat(chat_id):
    user_id = session.get('user_id')
    
    # Verify user is part of this private chat
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()
    c.execute('SELECT user1_id, user2_id FROM private_chats WHERE id = ?', (chat_id,))
    chat_info = c.fetchone()
    conn.close()

    if not chat_info or (user_id != chat_info[0] and user_id != chat_info[1]):
        return jsonify({'success': False, 'error': 'Unauthorized to access this private chat'}), 403

    messages = get_private_messages(chat_id)
    formatted_messages = []
    for sender_id, content, timestamp in messages:
        sender_profile = get_user_by_id(sender_id)
        sender_name = sender_profile[1] if sender_profile else "Unknown"
        sender_unique_id = sender_profile[2] if sender_profile else ""
        
        formatted_messages.append({
            'sender_id': sender_id,
            'sender_name': sender_name,
            'sender_unique_id': sender_unique_id,
            'content': content,
            'timestamp': timestamp,
            'is_user': (sender_id == user_id)
        })
    
    # Get partner's unique ID for display
    partner_id = chat_info[0] if chat_info[1] == user_id else chat_info[1]
    partner_profile = get_user_by_id(partner_id)
    partner_unique_id = partner_profile[2] if partner_profile else "Unknown"

    return jsonify({
        'success': True,
        'messages': formatted_messages,
        'chat_id': chat_id,
        'partner_unique_id': partner_unique_id
    })

# New Group Chat Routes
@app.route('/group_chats', methods=['GET'])
@login_required
def group_chats():
    user_id = session.get('user_id')
    chats = get_user_group_chats(user_id)
    return render_template('group_chats.html', group_chats=chats, session=session)

@app.route('/create_group_chat', methods=['POST'])
@login_required
def create_group_chat_route():
    data = request.json
    group_name = data.get('group_name')
    user_id = session.get('user_id')

    if not group_name:
        return jsonify({'success': False, 'error': 'Group name is required'}), 400
    
    group_chat_id = create_group_chat(group_name, user_id)
    if group_chat_id:
        return jsonify({'success': True, 'group_chat_id': group_chat_id, 'message': 'Group chat created successfully.'})
    else:
        return jsonify({'success': False, 'error': 'Failed to create group chat.'}), 500

@app.route('/add_group_member', methods=['POST'])
@login_required
def add_group_member_route():
    data = request.json
    group_chat_id = data.get('group_chat_id')
    member_username = data.get('member_username')

    if not group_chat_id or not member_username:
        return jsonify({'success': False, 'error': 'Missing parameters'}), 400

    member_user = get_user_by_unique_id(member_username)
    if not member_user:
        return jsonify({'success': False, 'error': 'User not found'}), 404
    
    member_id = member_user[0]

    # Check if current user is a member of the group chat
    user_id = session.get('user_id')
    group_members = get_group_members(group_chat_id)
    if not any(m[0] == user_id for m in group_members):
        return jsonify({'success': False, 'error': 'Unauthorized to add members to this group'}), 403

    success = add_group_member(group_chat_id, member_id)
    if success:
        return jsonify({'success': True, 'message': f'{member_username} added to group.'})
    else:
        return jsonify({'success': False, 'error': 'Failed to add member or user already in group.'}), 500

@app.route('/load_group_chat/<int:chat_id>', methods=['GET'])
@login_required
def load_group_chat(chat_id):
    user_id = session.get('user_id')

    # Verify user is a member of this group chat
    group_members = get_group_members(chat_id)
    if not any(m[0] == user_id for m in group_members):
        return jsonify({'success': False, 'error': 'Unauthorized to access this group chat'}), 403

    messages = get_group_messages(chat_id)
    formatted_messages = []
    for sender_id, sender_name, sender_unique_id, content, timestamp, is_ai_message in messages:
        formatted_messages.append({
            'sender_id': sender_id,
            'sender_name': sender_name,
            'sender_unique_id': sender_unique_id,
            'content': content,
            'timestamp': timestamp,
            'is_user': (sender_id == user_id),
            'is_ai_message': is_ai_message
        })
    
    group_chat_info = get_group_chat_by_id(chat_id)
    group_name = group_chat_info[1] if group_chat_info else "Unknown Group"

    return jsonify({
        'success': True,
        'messages': formatted_messages,
        'chat_id': chat_id,
        'group_name': group_name,
        'members': [{'id': m[0], 'name': m[1], 'unique_id': m[2]} for m in group_members]
    })

if __name__ == '__main__':
    app.run(debug=True, port=5000)
